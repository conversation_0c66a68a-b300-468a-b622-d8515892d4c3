<div>
    <div>
            <div class="card bg-white overflow-auto bg-opacity-75 rounded-2 border-0 shadow ms-3">
                <div class="card-header bg-opacity-75 text-white d-flex justify-content-between align-items-center" style="background: linear-gradient(90deg, #009BFF, #00D4FF);">
                    <h4 class="mb-0">Détails DMO - #{{ dmo.id }}</h4>
                </div>
                <div id="boite">
                    <div class="card-body bg-opacity-75 bg-light message-container" style="height: 35rem;--bs-bg-opacity: .50;" id="chatbox" >  
                        {% for commentaire in dmo.getCommentaires|reverse %}
                            {% if commentaire.getUser == app.user %}
                                <div class="m-1">
                                    <div class="d-flex justify-content-end">
                                        <div class="rounded-top-4 rounded-start-4 border-0 p-2 shadow-sm text-white" style="background: #218aff">
                                            <p class="my-auto" style="word-break: break-word">
                                                {{ commentaire.commentaire|nl2br }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <p style="user-select: none;"><small>{{ commentaire.getCreatedAt|date('d/m/Y H:i') }}</small></p>
                                    </div>
                                </div>
                            {% else %}
                                <div class="m-1">
                                    <p class="mb-0" style="user-select: none;"><small>{{ commentaire.getUser }}</small></p>
                                    <div class="d-flex">
                                        <div class="rounded-top-4 rounded-end-4 border-0 p-2 shadow-sm" style="background:rgb(226, 226, 226)">
                                            <p class="my-auto" style="word-break: break-word">
                                                {{ commentaire.commentaire }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="d-flex">
                                        <p style="user-select: none;"><small>{{commentaire.getState}} {{ commentaire.getCreatedAt|date('d/m/Y H:i') }}</small></p>
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                <form method="post" id="chat-form" class="card-footer bg-light border-top-0" style="user-select: none;--bs-bg-opacity: .50;">
                    <div class="form-group">
                        <textarea name="message" id="message" required rows="3" placeholder="Votre message..." class="form-control rounded-0 rounded-top-4 rounded-start-4 bg-white border"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary btn-sm float-end border-0 rounded-0 rounded-top-4 rounded-start-4 mt-2" id="btn-sub" style="background: #218aff; width: 3rem; height: 2.6rem">
                        <i id="icon-sent" style="font-size: 1.5rem" class="fa fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .card-body {
        overflow: auto;
        display: flex;
        flex-direction: column-reverse;
    }
</style>

<script>
    $('#chat-form').on('submit', function(e) {
        e.preventDefault();
        var $btn = $('#btn-sub');
        $btn.prop('disabled', true);
        var message = $.trim($('#message').val());
        if (!message) {
            $btn.prop('disabled', false);
            return;
        }
        $.ajax({
            url: "{{ path('app_dmo_add_comment') }}",
            method: "POST",
            data: {
                comment: message,
                dmoId: "{{ dmo.id }}"
            },
            dataType: "json",
            success: function(data) {
                if(data.status === "success") {
                    $('#chatbox').load(window.location.href + ' #chatbox > *');
                } else {
                    alert(data.message);
                }
                $btn.prop('disabled', false);
                $('#message').val('');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error("Error:", textStatus, errorThrown);
                $btn.prop('disabled', false);
            }
        });
    });
</script>