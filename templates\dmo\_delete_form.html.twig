<form method="post" action="{{ path('app_dmo_delete', {'id': dmo.id}) }}" class="delete_form">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ dmo.id) }}">
    <button type="submit" class="btn btn-outline border-0 p-0" title="Delete">
        <i class="me-2 fa fa-trash text-danger" ></i> Supprimer
    </button>
</form>

<style>
    .my-icon0:hover {
        --primary: #ff0000;
        --secondary: #ff0000;
    }
</style>

<script>
    document.querySelectorAll('.my-icon0').forEach(icon => {
        icon.addEventListener('mouseover', () => {
            icon.setAttribute('colors', 'primary:#ff0000,secondary:#ff0000');
        });
        icon.addEventListener('mouseout', () => {
            icon.setAttribute('colors', 'primary:#000000,secondary:#000000');
        });
    });

    $('.delete_form').on('submit', function(e) {
        e.preventDefault();
        Swal.fire({
            title: 'Êtes-vous sûr de vouloir supprimer cet article?',
            showCancelButton: true,
            confirmButtonText: `<i class="fas fa-trash-alt"></i> Oui`,
            cancelButtonText: `Non`,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                return this.submit();
            }
        });
    });
</script>
