<?php
// src/Controller/DMOKpiController.php

namespace App\Controller;

use App\Repository\DMORepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DMOKpiController extends AbstractController
{
    #[Route('/dmo/kpi', name: 'app_dmo_kpi')]
    public function index(DMORepository $dmoRepository): Response
    {
        $visibleHistory = 3;
        $now = new \DateTime();

        /*
         * SECTION 1 : DMOs ouvertes (status = true) pour Engineering
         */
        $openDmosEng = $dmoRepository->findBy(['status' => true, 'Type' => 'Engineering']);
        $filteredOpenEng = array_filter($openDmosEng, function ($dmo) use ($visibleHistory, $now) {
            $yearDiff = (int)$now->format('Y') - (int)$dmo->getDateInit()->format('Y');
            return $yearDiff <= $visibleHistory;
        });
        $openGroupedEng = [];
        foreach ($filteredOpenEng as $dmo) {
            $dateInit = $dmo->getDateInit();
            $year = $dateInit->format('Y');
            $monthNumeric = $dateInit->format('n'); 
            $monthKey = str_pad($monthNumeric, 2, '0', STR_PAD_LEFT);
            $key = $year . '-' . $monthKey;
            $monthName = $dateInit->format('M');
            
            if (!isset($openGroupedEng[$key])) {
                $openGroupedEng[$key] = [
                    'fy'        => $year,
                    'm'         => $monthNumeric,
                    'monthName' => $monthName,
                    'Energy'    => 0,
                    'Industry'  => 0,
                    'Aerospace' => 0,
                ];
            }
            $division = $dmo->getProductRange()->getDivision();
            if ($division === 'Energy') {
                $openGroupedEng[$key]['Energy']++;
            } elseif ($division === 'Industry') {
                $openGroupedEng[$key]['Industry']++;
            } elseif ($division === 'Aerospace') {
                $openGroupedEng[$key]['Aerospace']++;
            }
        }
        ksort($openGroupedEng);
        $openDataEng = $openGroupedEng;
        // Cumul global pour DMOs Engineering ouvertes
        $allOpenEng = $dmoRepository->findBy(['status' => true, 'Type' => 'Engineering']);
        $allGroupedEng = [];
        foreach ($allOpenEng as $dmo) {
            $yearMonth = $dmo->getDateInit()->format('Y-m');
            if (!isset($allGroupedEng[$yearMonth])) {
                $allGroupedEng[$yearMonth] = 0;
            }
            $allGroupedEng[$yearMonth]++;
        }
        ksort($allGroupedEng);
        $cumulativeEng = 0;
        $cumulativeByMonthEng = [];
        foreach ($allGroupedEng as $yearMonth => $count) {
            $cumulativeEng += $count;
            list($year, $monthNum) = explode('-', $yearMonth);
            $dateObj = \DateTime::createFromFormat('!m', $monthNum);
            $monthName = $dateObj->format('M');
            $cumulativeByMonthEng[$yearMonth] = [
                'fy' => $year,
                'monthName' => $monthName,
                'cumulative' => $cumulativeEng,
            ];
        }
        $openIndexedEng = [];
        foreach ($openDataEng as $group) {
            $key = $group['fy'] . '-' . str_pad($group['m'], 2, '0', STR_PAD_LEFT);
            $openIndexedEng[$key] = $group;
        }
        $dataTableOpenEng = [];
        foreach ($cumulativeByMonthEng as $yearMonth => $cumData) {
            $group = isset($openIndexedEng[$yearMonth])
                ? $openIndexedEng[$yearMonth]
                : [
                    'fy' => $cumData['fy'],
                    'monthName' => $cumData['monthName'],
                    'Energy' => 0,
                    'Industry' => 0,
                    'Aerospace' => 0,
                ];
            $dataTableOpenEng[] = [
                'periode'  => $group['fy'] . '-' . $group['monthName'],
                'Energy'   => $group['Energy'],
                'Industry' => $group['Industry'],
                'Aerospace'=> $group['Aerospace'],
                'Cumul'    => $cumData['cumulative'],
            ];
        }

        /*
         * SECTION 2 : DMOs fermées (status = false) pour Engineering
         */
        $closedDmosEng = $dmoRepository->findBy(['status' => false, 'Type' => 'Engineering']);
        $filteredClosedEng = array_filter($closedDmosEng, function ($dmo) use ($visibleHistory, $now) {
            $yearDiff = (int)$now->format('Y') - (int)$dmo->getDateInit()->format('Y');
            return $yearDiff <= $visibleHistory;
        });
        $closedGroupedEng = [];
        foreach ($filteredClosedEng as $dmo) {
            $dateInit = $dmo->getDateInit();
            $year = $dateInit->format('Y');
            $monthNumeric = $dateInit->format('n');
            $monthKey = str_pad($monthNumeric, 2, '0', STR_PAD_LEFT);
            $key = $year . '-' . $monthKey;
            $monthName = $dateInit->format('M');
            if (!isset($closedGroupedEng[$key])) {
                $closedGroupedEng[$key] = [
                    'fy'        => $year,
                    'm'         => $monthNumeric,
                    'monthName' => $monthName,
                    'Energy'    => 0,
                    'Industry'  => 0,
                    'Aerospace' => 0,
                ];
            }
            $division = $dmo->getProductRange()->getDivision();
            if ($division === 'Energy') {
                $closedGroupedEng[$key]['Energy']++;
            } elseif ($division === 'Industry') {
                $closedGroupedEng[$key]['Industry']++;
            } elseif ($division === 'Aerospace') {
                $closedGroupedEng[$key]['Aerospace']++;
            }
        }
        ksort($closedGroupedEng);
        $closedDataEng = $closedGroupedEng;
        // Cumul global pour DMOs Engineering fermées
        $allClosedEng = $dmoRepository->findBy(['status' => false, 'Type' => 'Engineering']);
        $allGroupedClosedEng = [];
        foreach ($allClosedEng as $dmo) {
            $yearMonth = $dmo->getDateInit()->format('Y-m');
            if (!isset($allGroupedClosedEng[$yearMonth])) {
                $allGroupedClosedEng[$yearMonth] = 0;
            }
            $allGroupedClosedEng[$yearMonth]++;
        }
        ksort($allGroupedClosedEng);
        $cumulativeClosedEng = 0;
        $cumulativeByMonthClosedEng = [];
        foreach ($allGroupedClosedEng as $yearMonth => $count) {
            $cumulativeClosedEng += $count;
            list($year, $monthNum) = explode('-', $yearMonth);
            $dateObj = \DateTime::createFromFormat('!m', $monthNum);
            $monthName = $dateObj->format('M');
            $cumulativeByMonthClosedEng[$yearMonth] = [
                'fy' => $year,
                'monthName' => $monthName,
                'cumulative' => $cumulativeClosedEng,
            ];
        }
        $closedIndexedEng = [];
        foreach ($closedDataEng as $group) {
            $key = $group['fy'] . '-' . str_pad($group['m'], 2, '0', STR_PAD_LEFT);
            $closedIndexedEng[$key] = $group;
        }
        $dataTableClosedEng = [];
        foreach ($cumulativeByMonthClosedEng as $yearMonth => $cumData) {
            $group = isset($closedIndexedEng[$yearMonth])
                ? $closedIndexedEng[$yearMonth]
                : [
                    'fy' => $cumData['fy'],
                    'monthName' => $cumData['monthName'],
                    'Energy' => 0,
                    'Industry' => 0,
                    'Aerospace' => 0,
                ];
            $dataTableClosedEng[] = [
                'periode'  => $group['fy'] . '-' . $group['monthName'],
                'Energy'   => $group['Energy'],
                'Industry' => $group['Industry'],
                'Aerospace'=> $group['Aerospace'],
                'Cumul'    => $cumData['cumulative'],
            ];
        }

        /*
         * SECTION 3 : DMOs ouvertes (status = true) pour les autres types (non Engineering)
         */
        $openDmosOther = $dmoRepository->findBy(['status' => true]);
        // Filtrer pour exclure Engineering
        $openDmosOther = array_filter($openDmosOther, function($dmo) {
            return $dmo->getType() !== 'Engineering';
        });
        $filteredOpenOther = array_filter($openDmosOther, function ($dmo) use ($visibleHistory, $now) {
            $yearDiff = (int)$now->format('Y') - (int)$dmo->getDateInit()->format('Y');
            return $yearDiff <= $visibleHistory;
        });
        $openGroupedOther = [];
        foreach ($filteredOpenOther as $dmo) {
            $dateInit = $dmo->getDateInit();
            $year = $dateInit->format('Y');
            $monthNumeric = $dateInit->format('n');
            $monthKey = str_pad($monthNumeric, 2, '0', STR_PAD_LEFT);
            $key = $year . '-' . $monthKey;
            $monthName = $dateInit->format('M');
            
            if (!isset($openGroupedOther[$key])) {
                $openGroupedOther[$key] = [
                    'fy'        => $year,
                    'm'         => $monthNumeric,
                    'monthName' => $monthName,
                    'Energy'    => 0,
                    'Industry'  => 0,
                    'Aerospace' => 0,
                ];
            }
            $division = $dmo->getProductRange()->getDivision();
            if ($division === 'Energy') {
                $openGroupedOther[$key]['Energy']++;
            } elseif ($division === 'Industry') {
                $openGroupedOther[$key]['Industry']++;
            } elseif ($division === 'Aerospace') {
                $openGroupedOther[$key]['Aerospace']++;
            }
        }
        ksort($openGroupedOther);
        $openDataOther = $openGroupedOther;
        // Cumul global pour toutes les DMOs non Engineering ouvertes
        $allOpenOther = $dmoRepository->findBy(['status' => true]);
        $allOpenOther = array_filter($allOpenOther, function($dmo) {
            return $dmo->getType() !== 'Engineering';
        });
        $allGroupedOther = [];
        foreach ($allOpenOther as $dmo) {
            $yearMonth = $dmo->getDateInit()->format('Y-m');
            if (!isset($allGroupedOther[$yearMonth])) {
                $allGroupedOther[$yearMonth] = 0;
            }
            $allGroupedOther[$yearMonth]++;
        }
        ksort($allGroupedOther);
        $cumulativeOther = 0;
        $cumulativeByMonthOther = [];
        foreach ($allGroupedOther as $yearMonth => $count) {
            $cumulativeOther += $count;
            list($year, $monthNum) = explode('-', $yearMonth);
            $dateObj = \DateTime::createFromFormat('!m', $monthNum);
            $monthName = $dateObj->format('M');
            $cumulativeByMonthOther[$yearMonth] = [
                'fy' => $year,
                'monthName' => $monthName,
                'cumulative' => $cumulativeOther,
            ];
        }
        $openIndexedOther = [];
        foreach ($openDataOther as $group) {
            $key = $group['fy'] . '-' . str_pad($group['m'], 2, '0', STR_PAD_LEFT);
            $openIndexedOther[$key] = $group;
        }
        $dataTableOpenOther = [];
        foreach ($cumulativeByMonthOther as $yearMonth => $cumData) {
            $group = isset($openIndexedOther[$yearMonth])
                ? $openIndexedOther[$yearMonth]
                : [
                    'fy' => $cumData['fy'],
                    'monthName' => $cumData['monthName'],
                    'Energy' => 0,
                    'Industry' => 0,
                    'Aerospace' => 0,
                ];
            $dataTableOpenOther[] = [
                'periode'  => $group['fy'] . '-' . $group['monthName'],
                'Energy'   => $group['Energy'],
                'Industry' => $group['Industry'],
                'Aerospace'=> $group['Aerospace'],
                'Cumul'    => $cumData['cumulative'],
            ];
        }

        /*
         * SECTION 4 : DMOs fermées (status = false) pour les autres types (non Engineering)
         */
        $closedDmosOther = $dmoRepository->findBy(['status' => false]);
        $closedDmosOther = array_filter($closedDmosOther, function($dmo) {
            return $dmo->getType() !== 'Engineering';
        });
        $filteredClosedOther = array_filter($closedDmosOther, function ($dmo) use ($visibleHistory, $now) {
            $yearDiff = (int)$now->format('Y') - (int)$dmo->getDateInit()->format('Y');
            return $yearDiff <= $visibleHistory;
        });
        $closedGroupedOther = [];
        foreach ($filteredClosedOther as $dmo) {
            $dateInit = $dmo->getDateInit();
            $year = $dateInit->format('Y');
            $monthNumeric = $dateInit->format('n');
            $monthKey = str_pad($monthNumeric, 2, '0', STR_PAD_LEFT);
            $key = $year . '-' . $monthKey;
            $monthName = $dateInit->format('M');
            if (!isset($closedGroupedOther[$key])) {
                $closedGroupedOther[$key] = [
                    'fy'        => $year,
                    'm'         => $monthNumeric,
                    'monthName' => $monthName,
                    'Energy'    => 0,
                    'Industry'  => 0,
                    'Aerospace' => 0,
                ];
            }
            $division = $dmo->getProductRange()->getDivision();
            if ($division === 'Energy') {
                $closedGroupedOther[$key]['Energy']++;
            } elseif ($division === 'Industry') {
                $closedGroupedOther[$key]['Industry']++;
            } elseif ($division === 'Aerospace') {
                $closedGroupedOther[$key]['Aerospace']++;
            }
        }
        ksort($closedGroupedOther);
        $closedDataOther = $closedGroupedOther;
        // Cumul global pour DMOs non Engineering fermées
        $allClosedOther = $dmoRepository->findBy(['status' => false]);
        $allClosedOther = array_filter($allClosedOther, function($dmo) {
            return $dmo->getType() !== 'Engineering';
        });
        $allGroupedClosedOther = [];
        foreach ($allClosedOther as $dmo) {
            $yearMonth = $dmo->getDateInit()->format('Y-m');
            if (!isset($allGroupedClosedOther[$yearMonth])) {
                $allGroupedClosedOther[$yearMonth] = 0;
            }
            $allGroupedClosedOther[$yearMonth]++;
        }
        ksort($allGroupedClosedOther);
        $cumulativeClosedOther = 0;
        $cumulativeByMonthClosedOther = [];
        foreach ($allGroupedClosedOther as $yearMonth => $count) {
            $cumulativeClosedOther += $count;
            list($year, $monthNum) = explode('-', $yearMonth);
            $dateObj = \DateTime::createFromFormat('!m', $monthNum);
            $monthName = $dateObj->format('M');
            $cumulativeByMonthClosedOther[$yearMonth] = [
                'fy' => $year,
                'monthName' => $monthName,
                'cumulative' => $cumulativeClosedOther,
            ];
        }
        $closedIndexedOther = [];
        foreach ($closedDataOther as $group) {
            $key = $group['fy'] . '-' . str_pad($group['m'], 2, '0', STR_PAD_LEFT);
            $closedIndexedOther[$key] = $group;
        }
        $dataTableClosedOther = [];
        foreach ($cumulativeByMonthClosedOther as $yearMonth => $cumData) {
            $group = isset($closedIndexedOther[$yearMonth])
                ? $closedIndexedOther[$yearMonth]
                : [
                    'fy' => $cumData['fy'],
                    'monthName' => $cumData['monthName'],
                    'Energy' => 0,
                    'Industry' => 0,
                    'Aerospace' => 0,
                ];
            $dataTableClosedOther[] = [
                'periode'  => $group['fy'] . '-' . $group['monthName'],
                'Energy'   => $group['Energy'],
                'Industry' => $group['Industry'],
                'Aerospace'=> $group['Aerospace'],
                'Cumul'    => $cumData['cumulative'],
            ];
        }
         
        return $this->render('dmo/kpi.html.twig', [
            'dataTableOpenEng'   => $dataTableOpenEng,
            'dataTableClosedEng' => $dataTableClosedEng,
            'dataTableOpenOther'   => $dataTableOpenOther,
            'dataTableClosedOther' => $dataTableClosedOther,
        ]);
    }
}
