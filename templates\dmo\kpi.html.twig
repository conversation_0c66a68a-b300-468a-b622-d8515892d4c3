{% extends 'base.html.twig' %}

{% block title %}KPI / Key Indicators{% endblock %}

{% block navbar %}
    {% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block body %}
<div class="container ms-5">
    <h1 class="title mt-3">KPI / Key Indicators</h1>


    <!-- Nav tabs -->
    <ul class="nav nav-tabs" id="dmoTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="open-eng-tab" data-bs-toggle="tab" data-bs-target="#open-eng" type="button" role="tab" aria-controls="open-eng" aria-selected="true">Open DMOs (Engineering)</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="closed-eng-tab" data-bs-toggle="tab" data-bs-target="#closed-eng" type="button" role="tab" aria-controls="closed-eng" aria-selected="false">Closed DMOs (Engineering)</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="open-other-tab" data-bs-toggle="tab" data-bs-target="#open-other" type="button" role="tab" aria-controls="open-other" aria-selected="false">Open DMOs (Non-Engineering)</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="closed-other-tab" data-bs-toggle="tab" data-bs-target="#closed-other" type="button" role="tab" aria-controls="closed-other" aria-selected="false">Closed DMOs (Non-Engineering)</button>
        </li>
    </ul>

    <!-- Tab content -->
    <div class="tab-content" id="dmoTabContent">
        <!-- Open DMOs Engineering -->
        <div class="tab-pane fade show active" id="open-eng" role="tabpanel" aria-labelledby="open-eng-tab">
            <h2 class="mt-3">Open DMOs (Engineering)</h2>
                <p class="description">
                    Number of DMO opened per issue date each year, month and division over the current year and the last 3 years.
                    A yearly cumulative total shows up as the curve on the graph.
                </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Eng. Energy</th>
                                <th>Eng. Industry</th>
                                <th>Eng. Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableOpenEng %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_open_engineering"></div>
                </div>
            </div>
        </div>

        <!-- Closed DMOs Engineering -->
        <div class="tab-pane fade" id="closed-eng" role="tabpanel" aria-labelledby="closed-eng-tab">
            <h2 class="mt-3">Closed DMOs (Engineering)</h2>
                <p class="description">
                    Number of DMO closed each month of the last 3 years.
                    A yearly cumulative total shows up as the curve on the graph.
                </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Eng. Energy</th>
                                <th>Eng. Industry</th>
                                <th>Eng. Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableClosedEng %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_closed_engineering"></div>
                </div>
            </div>
        </div>

        <!-- Open DMOs Non-Engineering -->
        <div class="tab-pane fade" id="open-other" role="tabpanel" aria-labelledby="open-other-tab">
            <h2 class="mt-3">Open DMOs (Non-Engineering)</h2>
            <p class="description">
                Number of DMO opened per issue date each year, month and division over the current year and the last 3 years.
                A yearly cumulative total shows up as the curve on the graph.
            </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Other Energy</th>
                                <th>Other Industry</th>
                                <th>Other Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableOpenOther %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_open_other"></div>
                </div>
            </div>
        </div>

        <!-- Closed DMOs Non-Engineering -->
        <div class="tab-pane fade" id="closed-other" role="tabpanel" aria-labelledby="closed-other-tab">
            <h2 class="mt-3">Closed DMOs (Non-Engineering)</h2>
            <p class="description">
                Number of DMO closed each month of the last 3 years.
                A yearly cumulative total shows up as the curve on the graph.
            </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Other Energy</th>
                                <th>Other Industry</th>
                                <th>Other Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableClosedOther %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_closed_other"></div>
                </div>
            </div>
        </div>
    </div>

<!-- Chargement des graphiques Google Charts -->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script>
    google.charts.load('current', {'packages':['corechart']});
    google.charts.setOnLoadCallback(drawCharts);

    function drawCharts() {
        drawOpenEngChart();
        drawClosedEngChart();
        drawOpenOtherChart();
        drawClosedOtherChart();
    }

    function drawOpenEngChart() {
        var data = google.visualization.arrayToDataTable([
            ['Période', 'Energy', 'Industry', 'Aerospace', 'Cumul'],
            {% for row in dataTableOpenEng %}
                ['{{ row.periode }}', {{ row.Energy }}, {{ row.Industry }}, {{ row.Aerospace }}, {{ row.Cumul }}],
            {% endfor %}
        ]);
        var options = {
            title: 'Open DMOs (Engineering)',
            vAxes: {
                0: { title: 'Monthly Open DMOs' },
                1: { title: 'Cumulative Open DMOs' }
            },
            seriesType: 'bars',
            isStacked: true,
            series: { 3: { type: 'line', targetAxisIndex: 1, color: '#d62728' } },
            hAxis: { title: 'Period' },
            legend: { position: 'top' },
            height: 400
        };
        var chart = new google.visualization.ComboChart(document.getElementById('chart_open_engineering'));
        chart.draw(data, options);
    }

    function drawClosedEngChart() {
        var data = google.visualization.arrayToDataTable([
            ['Période', 'Energy', 'Industry', 'Aerospace', 'Cumul'],
            {% for row in dataTableClosedEng %}
                ['{{ row.periode }}', {{ row.Energy }}, {{ row.Industry }}, {{ row.Aerospace }}, {{ row.Cumul }}],
            {% endfor %}
        ]);
        var options = {
            title: 'Closed DMOs (Engineering)',
            vAxes: {
                0: { title: 'Monthly Closed DMOs' },
                1: { title: 'Cumulative Closed DMOs' }
            },
            seriesType: 'bars',
            isStacked: true,
            series: { 3: { type: 'line', targetAxisIndex: 1, color: '#d62728' } },
            hAxis: { title: 'Period' },
            legend: { position: 'top' },
            height: 400
        };
        var chart = new google.visualization.ComboChart(document.getElementById('chart_closed_engineering'));
        chart.draw(data, options);
    }

    function drawOpenOtherChart() {
        var data = google.visualization.arrayToDataTable([
            ['Période', 'Energy', 'Industry', 'Aerospace', 'Cumul'],
            {% for row in dataTableOpenOther %}
                ['{{ row.periode }}', {{ row.Energy }}, {{ row.Industry }}, {{ row.Aerospace }}, {{ row.Cumul }}],
            {% endfor %}
        ]);
        var options = {
            title: 'Open DMOs (Non-Engineering)',
            vAxes: {
                0: { title: 'Monthly Open DMOs' },
                1: { title: 'Cumulative Open DMOs' }
            },
            seriesType: 'bars',
            isStacked: true,
            series: { 3: { type: 'line', targetAxisIndex: 1, color: '#d62728' } },
            hAxis: { title: 'Period' },
            legend: { position: 'top' },
            height: 400
        };
        var chart = new google.visualization.ComboChart(document.getElementById('chart_open_other'));
        chart.draw(data, options);
    }

    function drawClosedOtherChart() {
        var data = google.visualization.arrayToDataTable([
            ['Période', 'Energy', 'Industry', 'Aerospace', 'Cumul'],
            {% for row in dataTableClosedOther %}
                ['{{ row.periode }}', {{ row.Energy }}, {{ row.Industry }}, {{ row.Aerospace }}, {{ row.Cumul }}],
            {% endfor %}
        ]);
        var options = {
            title: 'Closed DMOs (Non-Engineering)',
            vAxes: {
                0: { title: 'Monthly Closed DMOs' },
                1: { title: 'Cumulative Closed DMOs' }
            },
            seriesType: 'bars',
            isStacked: true,
            series: { 3: { type: 'line', targetAxisIndex: 1, color: '#d62728' } },
            hAxis: { title: 'Period' },
            legend: { position: 'top' },
            height: 400
        };
        var chart = new google.visualization.ComboChart(document.getElementById('chart_closed_other'));
        chart.draw(data, options);
    }

    // Redessiner les graphiques lorsque l'onglet devient visible
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(function(tab) {
        tab.addEventListener('shown.bs.tab', function(event) {
            // On redessine tous les graphiques pour être sûr qu'ils s'affichent correctement
            drawOpenEngChart();
            drawClosedEngChart();
            drawOpenOtherChart();
            drawClosedOtherChart();
        });
    });
</script>
</div>

<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        font-family: Arial, sans-serif;
    }
    .title {
        text-align: left;
        color: #333;
        font-size: 20px;
        margin-bottom: 10px;
    }
    .description {
        font-size: 14px;
        color: #666;
        margin-bottom: 20px;
    }
    .content {
        display: flex;
        align-items: center;
        gap: 20px;
        margin: 20px 0;
    }
    .table-container {
        flex: 1;
        min-width: 50%;
    }
    .chart-container {
        flex: 2;
        min-width: 100%;
    }
    .styled-table {
        width: 100%;
        border-collapse: collapse;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    }
    .styled-table th, .styled-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    .styled-table thead {
        background-color: #222;
        color: white;
    }
    .styled-table tbody tr:hover {
        background-color: #f1f1f1;
    }
    #chart_open_engineering, #chart_closed_engineering, 
    #chart_open_other, #chart_closed_other {
        width: 100%!important;
        height: 400px;
    }
</style>
{% endblock %}
